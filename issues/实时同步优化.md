# 实时同步优化任务

## 背景需求

- 后端保持数据库与云端照片数目一致
- OSS 有几张照片，小程序就需要能预览几张照片
- 小程序点击下载，OSS 就要删除对应项目同时刷新数目

## 执行计划

1. WebSocket 服务设置 - 实现照片数量实时推送
2. 照片控制器增加实时通知 - 上传/下载/删除时推送更新
3. 同步服务模块 - 自动检查和修复数据不一致
4. 定时同步任务 - 每 5 分钟全量同步作为兜底
5. 小程序 WebSocket 集成 - 接收实时更新
6. 首页实时更新 - 无需手动刷新
7. 错误处理和日志优化 - 完善监控告警
8. 依赖安装 - ws, node-cron, winston

## 实施进度

- [x] 安装依赖
- [x] WebSocket 服务实现
- [x] 同步服务实现
- [x] 定时任务配置
- [x] 小程序端集成
- [ ] 测试验证

## 已完成的功能

1. **WebSocket 服务** (`src/websocket/photoSync.js`)

   - JWT 认证机制
   - 心跳检测
   - 实时推送照片数量更新

2. **同步服务** (`src/services/syncService.js`)

   - 自动检查数据一致性
   - 修复不一致数据
   - 失败重试机制（3 次）

3. **定时任务** (`src/tasks/syncTask.js`)

   - 每 5 分钟全量同步
   - 启动时立即执行一次
   - 提供手动触发 API

4. **日志系统** (`src/utils/logger.js`)

   - 分类日志（错误、同步、OSS）
   - 日志文件轮转
   - 错误告警机制

5. **小程序集成**
   - WebSocket 自动重连
   - 实时更新照片数量
   - 断线后降级处理

## 下一步

需要测试验证整个实时同步流程是否正常工作。
