# 修复 OSS 照片下载后删除问题

## 问题描述

在照片分享小程序中，当用户点击下载照片时，系统应在用户下载后自动删除阿里云 OSS 中的对应文件。但目前存在问题：点击下载后阿里云 OSS 中的照片没有被成功删除。

## 原因分析

通过代码分析发现，当前实现使用 `setTimeout` 延迟 5 分钟后删除 OSS 文件。这种方式存在以下问题：

1. 如果服务器在 5 分钟内重启，setTimeout 任务会丢失
2. Node.js 进程崩溃会导致 setTimeout 任务丢失
3. 延迟删除不符合"阅后即焚"的即时性要求

## 修改计划

修改 photoController.js 中的 downloadAndDelete 函数，实现点击下载后立即删除 OSS 文件：

1. 移除 setTimeout 延迟删除机制
2. 调整代码顺序，确保下载 URL 生成后立即执行 OSS 删除
3. 确保即使 OSS 删除失败，也不会影响用户下载体验
4. 保证已生成的签名 URL 在 OSS 文件删除后仍然有效

## 执行步骤

1. 修改 `photo-share-miniapp/server/src/controllers/photoController.js` 文件中的 `downloadAndDelete` 函数
2. 去除 setTimeout 延迟删除逻辑
3. 调整代码结构，优化错误处理
