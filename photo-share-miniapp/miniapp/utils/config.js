// 配置文件
const isDev = true; // 是否是开发环境

const config = {
  // API基础URL
  apiUrl: isDev ? "http://localhost:3000/api" : "https://your-domain.com/api",

  // WebSocket URL
  wsUrl: isDev ? "ws://localhost:3000" : "wss://your-domain.com",

  // 请求超时时间
  timeout: 30000,

  // 文件上传大小限制 (10MB)
  maxFileSize: 10 * 1024 * 1024,

  // 支持的图片类型
  supportedImageTypes: ["jpg", "jpeg", "png", "gif", "webp"],
};

module.exports = config;
