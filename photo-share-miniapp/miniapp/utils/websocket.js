const config = require("./config");

class PhotoSyncWebSocket {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 3000; // 3秒
    this.listeners = new Map();
    this.heartbeatTimer = null;
  }

  // 连接WebSocket
  connect() {
    if (this.ws && this.isConnected) {
      console.log("WebSocket已连接，无需重复连接");
      return;
    }

    const token = wx.getStorageSync("token");
    if (!token) {
      console.error("没有找到认证token，无法连接WebSocket");
      return;
    }

    const wsUrl = `${config.wsUrl}/ws/photo-sync`;
    console.log("正在连接WebSocket:", wsUrl);

    this.ws = wx.connectSocket({
      url: wsUrl,
      success: () => {
        console.log("WebSocket连接请求已发送");
      },
      fail: (error) => {
        console.error("WebSocket连接失败:", error);
        this.scheduleReconnect();
      },
    });

    // 监听WebSocket事件
    this.ws.onOpen(() => {
      console.log("WebSocket连接已建立");
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // 发送认证信息
      this.send({
        type: "auth",
        token: token,
      });

      // 启动心跳
      this.startHeartbeat();

      // 触发连接成功事件
      this.emit("connected");
    });

    this.ws.onMessage((res) => {
      try {
        const data = JSON.parse(res.data);
        console.log("收到WebSocket消息:", data);

        // 处理不同类型的消息
        switch (data.type) {
          case "auth_success":
            console.log("WebSocket认证成功");
            this.emit("auth_success", data);
            break;

          case "photo_count_update":
            console.log("收到照片数量更新:", data.data);
            this.emit("photo_count_update", data.data);
            break;

          case "sync_status":
            console.log("收到同步状态:", data.data);
            this.emit("sync_status", data.data);
            break;

          case "error":
            console.error("WebSocket错误:", data.message);
            this.emit("error", data);
            break;

          default:
            console.log("未知消息类型:", data.type);
        }
      } catch (error) {
        console.error("处理WebSocket消息失败:", error);
      }
    });

    this.ws.onError((error) => {
      console.error("WebSocket错误:", error);
      this.isConnected = false;
      this.emit("error", error);
    });

    this.ws.onClose(() => {
      console.log("WebSocket连接已关闭");
      this.isConnected = false;
      this.stopHeartbeat();
      this.emit("disconnected");

      // 尝试重连
      this.scheduleReconnect();
    });
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.stopHeartbeat();
  }

  // 发送消息
  send(data) {
    if (!this.isConnected || !this.ws) {
      console.error("WebSocket未连接，无法发送消息");
      return false;
    }

    this.ws.send({
      data: JSON.stringify(data),
      success: () => {
        console.log("发送WebSocket消息成功:", data);
      },
      fail: (error) => {
        console.error("发送WebSocket消息失败:", error);
      },
    });

    return true;
  }

  // 心跳机制
  startHeartbeat() {
    this.stopHeartbeat();

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: "ping" });
      }
    }, 30000); // 30秒一次心跳
  }

  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 重连机制
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error("WebSocket重连次数超过限制，停止重连");
      this.emit("reconnect_failed");
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;

    console.log(
      `将在 ${delay / 1000} 秒后尝试第 ${this.reconnectAttempts} 次重连`
    );

    setTimeout(() => {
      console.log(`开始第 ${this.reconnectAttempts} 次重连`);
      this.connect();
    }, delay);
  }

  // 事件监听
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event);
    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
    }
  }

  emit(event, data) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event);
    callbacks.forEach((callback) => {
      try {
        callback(data);
      } catch (error) {
        console.error(`事件处理器错误 [${event}]:`, error);
      }
    });
  }

  // 获取连接状态
  getStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}

// 创建单例
const photoSyncWS = new PhotoSyncWebSocket();

module.exports = photoSyncWS;
