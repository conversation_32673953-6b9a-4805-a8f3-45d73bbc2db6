/* 全屏滚动容器 */
.page-scroll {
  height: 100vh;
  background: #f5f7fa;
}

/* 顶部统计区域容器 */
.stats-section {
  position: sticky;
  top: 0;
  z-index: 100;
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
  margin: 0 8rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
}

/* 统计栏美化 - 紫色渐变，贴近屏幕边缘 */
.stats-bar {
  display: flex;
  justify-content: space-around;
  padding: 50rpx 16rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  border-radius: 0 0 24rpx 24rpx;
}

.stats-bar::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

.stat-item {
  text-align: center;
  flex: 1;
  position: relative;
  z-index: 1;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 主要内容区域 */
.content-section {
  padding: 0 8rpx;
  min-height: calc(100vh - 200rpx);
}

/* 数据一致性警告样式 */
.consistency-warning {
  background: linear-gradient(135deg, #fff5e6 0%, #ffecd1 100%);
  color: #e6a23c;
  text-align: center;
  padding: 24rpx 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin: 16rpx 8rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(230, 162, 60, 0.1);
}

.consistency-warning::before {
  content: "⚠️";
  font-size: 28rpx;
}

/* 操作栏美化 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  background: white;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  gap: 40rpx;
  margin: 16rpx 8rpx;
  border-radius: 20rpx;
}

.select-all {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.select-all checkbox {
  margin-right: 12rpx;
  transform: scale(1.1);
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(255, 71, 87, 0.25);
  transition: all 0.3s ease;
  margin-left: auto;
  flex-shrink: 0;
}

.delete-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 12rpx rgba(255, 71, 87, 0.25);
}

/* 照片网格布局 */
.photo-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 16rpx 8rpx 32rpx;
  gap: 16rpx;
  justify-content: space-between;
}

.photo-item {
  width: calc(50% - 8rpx);
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 16rpx;
}

.photo-item:active {
  transform: scale(0.97);
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.08);
}

.photo-checkbox {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.photo-thumb {
  width: 100%;
  height: 180rpx;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 24rpx 24rpx 0 0;
}

.photo-item:active .photo-thumb {
  transform: scale(1.02);
}

.photo-info {
  padding: 16rpx 20rpx;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(249, 250, 251, 1) 100%
  );
}

.photo-name {
  display: block;
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.photo-size {
  font-size: 22rpx;
  color: #95a5a6;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.photo-size::before {
  content: "📦";
  font-size: 18rpx;
}

.photo-actions {
  padding: 0 16rpx 16rpx;
}

.action-btn {
  width: 100%;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-btn {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  color: white;
  border: none;
  box-shadow: 0 6rpx 18rpx rgba(46, 204, 113, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.download-btn::before {
  content: "⬇️";
  font-size: 24rpx;
}

.download-btn:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 10rpx rgba(46, 204, 113, 0.25);
}

/* 加载更多美化 */
.load-more {
  text-align: center;
  padding: 40rpx 32rpx 60rpx;
  color: #7f8c8d;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin: 0 16rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.load-more::before,
.load-more::after {
  content: "";
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, transparent, #e0e0e0, transparent);
}

/* 空状态美化 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx 80rpx;
  text-align: center;
  margin: 20rpx 16rpx;
  background: white;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.empty-icon {
  font-size: 140rpx;
  margin-bottom: 40rpx;
  display: block;
  animation: float 3s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-16rpx);
  }
}

.empty-state text {
  font-size: 30rpx;
  color: #7f8c8d;
  margin-bottom: 40rpx;
  line-height: 1.5;
  font-weight: 500;
}

.upload-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 28rpx;
  padding: 20rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.25);
}

/* WebSocket连接状态指示器 */
.ws-status {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #e74c3c;
  box-shadow: 0 0 10rpx rgba(231, 76, 60, 0.5);
  z-index: 100;
}

.ws-status.connected {
  background: #2ecc71;
  box-shadow: 0 0 10rpx rgba(46, 204, 113, 0.5);
  animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.9);
  }
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (min-width: 768rpx) {
  .photo-item {
    width: calc(33.333% - 12rpx);
  }

  .stats-bar {
    padding: 60rpx 24rpx 50rpx;
  }

  .stat-value {
    font-size: 52rpx;
  }
}

/* 小屏幕优化 */
@media screen and (max-width: 600rpx) {
  .stats-bar {
    padding: 40rpx 12rpx 32rpx;
  }

  .stat-value {
    font-size: 44rpx;
  }

  .stat-label {
    font-size: 24rpx;
  }

  .photo-thumb {
    height: 160rpx;
  }
}

/* 确保页面滚动流畅 */
.page-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 优化触摸反馈 */
.photo-item,
.action-btn,
.delete-btn,
.upload-btn {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}
