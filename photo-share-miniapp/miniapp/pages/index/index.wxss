.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 统计栏美化 */
.stats-bar {
  display: flex;
  justify-content: space-around;
  padding: 45rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.stats-bar::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

.stat-item {
  text-align: center;
  flex: 1;
  position: relative;
  z-index: 1;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 数据一致性警告样式 */
.consistency-warning {
  background: linear-gradient(135deg, #fff5e6 0%, #ffecd1 100%);
  color: #e6a23c;
  text-align: center;
  padding: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.consistency-warning::before {
  content: "⚠️";
  font-size: 28rpx;
}

/* 操作栏美化 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
  gap: 40rpx;
}

.select-all {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.select-all checkbox {
  margin-right: 12rpx;
  transform: scale(1.1);
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  color: white;
  border: none;
  padding: 16rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 15rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
  margin-left: auto;
  flex-shrink: 0;
}

.delete-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

/* 照片列表美化 */
.photo-list {
  flex: 1;
  background: #f5f7fa;
  overflow-y: auto;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  gap: 20rpx;
}

.photo-item {
  width: calc(50% - 10rpx);
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.3s ease;
}

.photo-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.photo-checkbox {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.photo-thumb {
  width: 100%;
  height: 160rpx;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-item:hover .photo-thumb {
  transform: scale(1.05);
}

.photo-info {
  padding: 20rpx;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 0%,
    rgba(249, 250, 251, 1) 100%
  );
}

.photo-name {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-size {
  font-size: 24rpx;
  color: #95a5a6;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.photo-size::before {
  content: "📦";
  font-size: 20rpx;
}

.photo-actions {
  padding: 0 20rpx 20rpx;
}

.action-btn {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.download-btn {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  color: white;
  border: none;
  box-shadow: 0 4rpx 15rpx rgba(46, 204, 113, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.download-btn::before {
  content: "⬇️";
  font-size: 28rpx;
}

.download-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(46, 204, 113, 0.3);
}

/* 加载更多美化 */
.load-more {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #7f8c8d;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.load-more::before,
.load-more::after {
  content: "";
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, transparent, #ddd, transparent);
}

/* 空状态美化 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 60rpx;
  text-align: center;
}

.empty-icon {
  font-size: 160rpx;
  margin-bottom: 50rpx;
  display: block;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

.empty-state text {
  font-size: 32rpx;
  color: #7f8c8d;
  margin-bottom: 50rpx;
  line-height: 1.6;
}

.empty-state button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.empty-state button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

/* WebSocket连接状态指示器 */
.ws-status {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #e74c3c;
  box-shadow: 0 0 10rpx rgba(231, 76, 60, 0.5);
  z-index: 100;
}

.ws-status.connected {
  background: #2ecc71;
  box-shadow: 0 0 10rpx rgba(46, 204, 113, 0.5);
  animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.9);
  }
}
