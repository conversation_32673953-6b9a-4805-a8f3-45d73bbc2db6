<!-- 全屏滚动容器 -->
<scroll-view
  class="page-scroll"
  scroll-y
  bindscrolltolower="loadMore"
  lower-threshold="100"
  enhanced="{{true}}"
  show-scrollbar="{{false}}"
>
  <!-- 顶部统计区域 - 紫色渐变 -->
  <view class="stats-section">
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-value">{{totalPhotos}}</text>
        <text class="stat-label">数据库照片</text>
      </view>
      <view class="stat-item" wx:if="{{showOssStats}}">
        <text class="stat-value">{{ossStats.totalPhotos}}</text>
        <text class="stat-label">云端照片</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{totalSize}}</text>
        <text class="stat-label">总大小</text>
      </view>
    </view>
  </view>

  <!-- 数据一致性提示 -->
  <view class="consistency-warning" wx:if="{{showOssStats && !isConsistent}}">
    <text>⚠️ 数据库与云端照片数量不一致</text>
  </view>

  <!-- 主要内容区域 -->
  <view class="content-section">
    <!-- 操作栏 -->
    <view class="action-bar" wx:if="{{photos.length > 0}}">
      <view class="action-left">
        <checkbox-group bindchange="handleSelectAll">
          <label class="select-all">
            <checkbox value="all" checked="{{selectAll}}"/>
            <text>全选</text>
          </label>
        </checkbox-group>
      </view>
      <view class="action-right">
        <button
          class="delete-btn"
          size="mini"
          type="warn"
          disabled="{{selectedPhotos.length === 0}}"
          bindtap="handleBatchDelete"
        >
          删除选中({{selectedPhotos.length}})
        </button>
      </view>
    </view>

    <!-- 照片网格 -->
    <view class="photo-grid" wx:if="{{photos.length > 0}}">
      <view
        class="photo-item"
        wx:for="{{photos}}"
        wx:key="id"
      >
        <checkbox-group bindchange="handlePhotoSelect" data-id="{{item.id}}">
          <label class="photo-checkbox">
            <checkbox value="{{item.id}}" checked="{{item.selected}}"/>
          </label>
        </checkbox-group>

        <image
          class="photo-thumb"
          src="{{item.thumbnailUrl}}"
          mode="aspectFill"
          data-index="{{index}}"
          bindtap="previewPhoto"
          lazy-load
        />

        <view class="photo-info">
          <text class="photo-name">{{item.original_name}}</text>
          <text class="photo-size">{{item.sizeText}}</text>
        </view>

        <view class="photo-actions">
          <button
            class="action-btn download-btn"
            size="mini"
            data-id="{{item.id}}"
            bindtap="downloadPhoto"
          >
            下载
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && photos.length === 0}}">
      <text class="empty-icon">📷</text>
      <text>还没有上传照片</text>
      <button class="upload-btn" bindtap="goToUpload">去上传</button>
    </view>
  </view>
</scroll-view>
