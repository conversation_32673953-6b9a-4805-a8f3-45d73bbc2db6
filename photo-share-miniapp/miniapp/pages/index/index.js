const request = require("../../utils/request");
const photoSyncWS = require("../../utils/websocket");

Page({
  data: {
    photos: [],
    totalPhotos: 0,
    totalSize: "0 MB",
    ossStats: {
      totalPhotos: 0,
      totalSize: "0 MB",
    },
    isConsistent: true,
    page: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    selectAll: false,
    selectedPhotos: [],
    showOssStats: false,
    wsConnected: false,
  },

  onLoad() {
    this.checkAuth();
    this.initWebSocket();
  },

  onShow() {
    if (wx.getStorageSync("token")) {
      this.resetAndLoad();
    }
  },

  onUnload() {
    // 页面卸载时断开WebSocket连接
    photoSyncWS.disconnect();
  },

  initWebSocket() {
    const token = wx.getStorageSync("token");
    if (!token) return;

    // 连接WebSocket
    photoSyncWS.connect();

    // 监听连接成功
    photoSyncWS.on("connected", () => {
      console.log("WebSocket连接成功");
      this.setData({ wsConnected: true });
    });

    // 监听认证成功
    photoSyncWS.on("auth_success", (data) => {
      console.log("WebSocket认证成功", data);
    });

    // 监听照片数量更新
    photoSyncWS.on("photo_count_update", (data) => {
      console.log("收到照片数量更新", data);

      // 更新页面数据
      this.setData({
        totalPhotos: data.totalPhotos,
        totalSize: this.formatFileSize(data.totalSize),
        ossStats: {
          totalPhotos: data.ossPhotos,
          totalSize: this.formatFileSize(data.ossSize),
        },
        isConsistent: data.isConsistent,
        showOssStats: true,
      });

      // 如果数据不一致，显示提示
      if (!data.isConsistent) {
        wx.showToast({
          title: "检测到数据不一致，正在自动修复",
          icon: "none",
          duration: 3000,
        });
      }
    });

    // 监听同步状态
    photoSyncWS.on("sync_status", (data) => {
      console.log("收到同步状态", data);
      if (data.status === "syncing") {
        wx.showToast({
          title: "正在同步数据...",
          icon: "loading",
          duration: 1500,
        });
      }
    });

    // 监听连接断开
    photoSyncWS.on("disconnected", () => {
      console.log("WebSocket连接断开");
      this.setData({ wsConnected: false });
    });

    // 监听错误
    photoSyncWS.on("error", (error) => {
      console.error("WebSocket错误", error);
    });
  },

  formatFileSize(bytes) {
    if (!bytes) return "0 MB";
    const mb = bytes / (1024 * 1024);
    return mb.toFixed(2) + " MB";
  },

  checkAuth() {
    const token = wx.getStorageSync("token");
    if (!token) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
    } else {
      this.loadPhotos();
    }
  },

  resetAndLoad() {
    this.setData({
      photos: [],
      page: 1,
      hasMore: true,
    });
    this.loadPhotos();
  },

  async loadPhotoStats() {
    try {
      const stats = await request.get("/photos/stats");

      this.setData({
        ossStats: {
          totalPhotos: stats.oss.totalPhotos,
          totalSize: stats.oss.totalSizeFormatted,
        },
        isConsistent: stats.isConsistent,
        showOssStats: true, // 成功获取后显示OSS统计
      });

      // 如果数据库和OSS数据不一致，显示提示
      if (!stats.isConsistent) {
        wx.showToast({
          title: "照片数据不一致，请联系管理员",
          icon: "none",
          duration: 3000,
        });
      }
    } catch (error) {
      console.error("加载照片统计信息失败", error);
      // 出错时不显示OSS统计
      this.setData({
        showOssStats: false,
      });
    }
  },

  async loadPhotos() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const res = await request.get("/photos", {
        page: this.data.page,
        pageSize: this.data.pageSize,
      });

      const photos = res.photos.map((photo) => ({
        ...photo,
        selected: false,
      }));

      this.setData({
        photos: [...this.data.photos, ...photos],
        totalPhotos: res.pagination.total,
        totalSize: this.formatTotalSize(res.pagination.totalSize),
        page: this.data.page + 1,
        hasMore: this.data.page < res.pagination.totalPages,
        loading: false,
        // 如果没有OSS统计信息，则直接设置与数据库一致
        ossStats: this.data.showOssStats
          ? this.data.ossStats
          : {
              totalPhotos: res.pagination.total,
              totalSize: this.formatTotalSize(res.pagination.totalSize),
            },
      });

      // 加载照片成功后再获取统计信息
      if (this.data.page === 2) {
        // 仅在第一页加载完成后获取统计信息
        this.loadPhotoStats();
      }
    } catch (error) {
      wx.showToast({
        title: error.message || "加载失败",
        icon: "none",
      });
      this.setData({ loading: false });
    }
  },

  loadMore() {
    this.loadPhotos();
  },

  async previewPhoto(e) {
    const { index } = e.currentTarget.dataset;
    const photo = this.data.photos[index];

    wx.showLoading({ title: "加载中..." });

    try {
      // 首先尝试获取OSS中的所有照片
      let ossPhotos = [];
      try {
        const ossResult = await request.get("/photos/oss-previews");
        ossPhotos = ossResult.ossPhotos || [];
        console.log(`从OSS获取到 ${ossPhotos.length} 张照片用于预览`);
      } catch (ossError) {
        console.error("获取OSS照片预览失败，将使用数据库照片", ossError);
      }

      // 如果无法获取OSS照片，则回退到数据库照片
      if (ossPhotos.length === 0) {
        // 获取当前照片的预览URL
        const res = await request.get(`/photos/${photo.id}/preview-url`);

        // 获取所有照片的预览URL
        const urls = await Promise.all(
          this.data.photos.map((p) =>
            request
              .get(`/photos/${p.id}/preview-url`)
              .then((r) => r.originalUrl)
          )
        );

        wx.hideLoading();
        wx.previewImage({
          current: res.originalUrl,
          urls: urls,
        });
      } else {
        // 使用OSS照片进行预览
        const urls = ossPhotos.map((p) => p.originalUrl);

        // 找到当前照片在OSS中的对应项
        let currentUrl = "";
        const matchingOssPhoto = ossPhotos.find(
          (p) => p.ossKey === photo.oss_key
        );
        if (matchingOssPhoto) {
          currentUrl = matchingOssPhoto.originalUrl;
        } else {
          // 如果找不到匹配项，则使用第一张照片
          currentUrl = urls[0];
        }

        wx.hideLoading();
        wx.previewImage({
          current: currentUrl,
          urls: urls,
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: "预览失败",
        icon: "none",
      });
      console.error("照片预览失败", error);
    }
  },

  async downloadPhoto(e) {
    const { id } = e.currentTarget.dataset;

    wx.showModal({
      title: "确认下载",
      content: "下载后将从服务器删除该照片，是否继续？",
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: "准备下载..." });

          try {
            const result = await request.post(`/photos/${id}/download`);

            wx.downloadFile({
              url: result.downloadUrl,
              success: (downloadRes) => {
                wx.hideLoading();

                // 保存到相册
                wx.saveImageToPhotosAlbum({
                  filePath: downloadRes.tempFilePath,
                  success: () => {
                    wx.showToast({
                      title: "已保存到相册",
                      icon: "success",
                    });

                    // 从列表中移除
                    const photos = this.data.photos.filter((p) => p.id !== id);
                    this.setData({
                      photos,
                      // 如果WebSocket已连接，不要手动更新数量，等待服务器推送
                      totalPhotos: this.data.wsConnected
                        ? this.data.totalPhotos
                        : this.data.totalPhotos - 1,
                    });

                    // WebSocket会自动推送更新，无需手动调用
                    // this.loadPhotoStats();

                    // 仅在WebSocket未连接时，手动更新本地数据
                    if (!this.data.wsConnected) {
                      this.setData({
                        totalPhotos: this.data.totalPhotos - 1,
                        ossStats: {
                          totalPhotos: this.data.totalPhotos - 1,
                          totalSize: this.data.totalSize,
                        },
                      });
                    }
                  },
                  fail: () => {
                    wx.showToast({
                      title: "保存失败，请检查相册权限",
                      icon: "none",
                    });
                  },
                });
              },
              fail: () => {
                wx.hideLoading();
                wx.showToast({
                  title: "下载失败",
                  icon: "none",
                });
              },
            });
          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || "操作失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  handlePhotoSelect(e) {
    const { id } = e.currentTarget.dataset;
    const { value } = e.detail;
    const selected = value.length > 0;

    const photos = this.data.photos.map((photo) => {
      if (photo.id === parseInt(id)) {
        return { ...photo, selected };
      }
      return photo;
    });

    const selectedPhotos = photos.filter((p) => p.selected).map((p) => p.id);

    this.setData({
      photos,
      selectedPhotos,
      selectAll: selectedPhotos.length === photos.length,
    });
  },

  handleSelectAll(e) {
    const { value } = e.detail;
    const selectAll = value.includes("all");

    const photos = this.data.photos.map((photo) => ({
      ...photo,
      selected: selectAll,
    }));

    const selectedPhotos = selectAll ? photos.map((p) => p.id) : [];

    this.setData({
      photos,
      selectedPhotos,
      selectAll,
    });
  },

  async handleBatchDelete() {
    const { selectedPhotos } = this.data;

    if (selectedPhotos.length === 0) return;

    wx.showModal({
      title: "确认删除",
      content: `确定要删除选中的 ${selectedPhotos.length} 张照片吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: "删除中..." });

          try {
            await request.delete("/photos/batch", {
              photoIds: selectedPhotos,
            });

            wx.hideLoading();
            wx.showToast({
              title: "删除成功",
              icon: "success",
            });

            // 从列表中移除
            const photos = this.data.photos.filter(
              (p) => !selectedPhotos.includes(p.id)
            );
            this.setData({
              photos,
              selectedPhotos: [],
              selectAll: false,
              // 如果WebSocket已连接，不要手动更新数量，等待服务器推送
              totalPhotos: this.data.wsConnected
                ? this.data.totalPhotos
                : this.data.totalPhotos - selectedPhotos.length,
            });

            // WebSocket会自动推送更新，无需手动调用
            // this.loadPhotoStats();

            // 仅在WebSocket未连接时，手动更新本地数据
            if (!this.data.wsConnected) {
              this.setData({
                totalPhotos: this.data.totalPhotos - selectedPhotos.length,
                ossStats: {
                  totalPhotos: this.data.totalPhotos - selectedPhotos.length,
                  totalSize: this.data.totalSize,
                },
              });
            }
          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || "删除失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  goToUpload() {
    wx.switchTab({
      url: "/pages/upload/upload",
    });
  },

  formatTotalSize(bytes) {
    if (!bytes) return "0 MB";
    const mb = bytes / (1024 * 1024);
    return mb.toFixed(2) + " MB";
  },
});
