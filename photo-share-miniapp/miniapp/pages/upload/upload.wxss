.container {
  padding: 16rpx 12rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
}

.upload-area {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  padding: 60rpx 32rpx;
  text-align: center;
  border: 3rpx dashed #e0e6ed;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.upload-icon {
  font-size: 140rpx;
  margin-bottom: 32rpx;
  animation: float 3s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-12rpx);
  }
}

.upload-text {
  display: block;
  font-size: 34rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.upload-desc {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.upload-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.25);
}

.preview-area {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.preview-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.preview-image:active {
  transform: scale(0.98);
}

.file-info {
  margin-bottom: 24rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  padding: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #667eea;
  width: 100rpx;
  font-weight: 600;
  font-size: 24rpx;
}

.info-value {
  color: #2c3e50;
  flex: 1;
  font-weight: 500;
}

.upload-progress {
  margin-bottom: 24rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  padding: 20rpx;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 26rpx;
  color: #667eea;
  margin-top: 12rpx;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  border: none;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.btn-primary {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(46, 204, 113, 0.25);
}

.btn-primary:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 12rpx rgba(46, 204, 113, 0.25);
}

.btn-secondary {
  background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%);
  color: #2c3e50;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.btn-secondary:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.btn[disabled] {
  opacity: 0.5;
  transform: none !important;
}

.tips {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: rgba(102, 126, 234, 0.03);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item:active {
  background: rgba(102, 126, 234, 0.06);
  transform: scale(0.98);
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  opacity: 0.8;
}

.tip-text {
  font-size: 26rpx;
  color: #2c3e50;
  flex: 1;
  line-height: 1.4;
  font-weight: 500;
}

/* 文件名输入框样式 */
.filename-input {
  flex: 1;
  padding: 12rpx 16rpx;
  border: 2rpx solid #e8ecf0;
  border-radius: 12rpx;
  font-size: 26rpx;
  background-color: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.filename-input:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-1rpx);
}

.filename-input[disabled] {
  background-color: #f8f9fa;
  color: #95a5a6;
  border-color: #e8ecf0;
}

/* 响应式设计 */
@media screen and (min-width: 768rpx) {
  .container {
    padding: 24rpx 20rpx;
  }

  .upload-area {
    padding: 80rpx 48rpx;
  }

  .preview-area {
    padding: 40rpx 32rpx;
  }

  .tips {
    padding: 40rpx 32rpx;
  }
}

/* 小屏幕优化 */
@media screen and (max-width: 600rpx) {
  .container {
    padding: 12rpx 8rpx;
  }

  .upload-area {
    padding: 48rpx 24rpx;
  }

  .upload-icon {
    font-size: 120rpx;
  }

  .upload-text {
    font-size: 30rpx;
  }

  .upload-desc {
    font-size: 24rpx;
  }
}

/* 触摸优化 */
.upload-area,
.preview-area,
.tips,
.btn,
.tip-item {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* 进度条美化 */
progress {
  border-radius: 12rpx;
  overflow: hidden;
}

/* 页面滚动优化 */
page {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}
