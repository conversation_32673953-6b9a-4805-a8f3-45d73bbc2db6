.container {
  padding: 40rpx;
  min-height: 100vh;
  background: #f8f9fa;
}

.upload-area {
  background: white;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  border: 2rpx dashed #ddd;
  margin-bottom: 40rpx;
}

.upload-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.upload-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.upload-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.upload-btn {
  background: #07c160;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 60rpx;
  font-size: 28rpx;
}

.preview-area {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.preview-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.file-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.info-label {
  color: #666;
  width: 120rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.upload-progress {
  margin-bottom: 30rpx;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 24rpx;
  color: #666;
  margin-top: 15rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.btn-primary {
  background: #07c160;
  color: white;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
}

.btn[disabled] {
  opacity: 0.6;
}

.tips {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 文件名输入框样式 */
.filename-input {
  flex: 1;
  padding: 8rpx 16rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  transition: border-color 0.3s;
}

.filename-input:focus {
  border-color: #07c160;
  outline: none;
}

.filename-input[disabled] {
  background-color: #f5f5f5;
  color: #999;
}
