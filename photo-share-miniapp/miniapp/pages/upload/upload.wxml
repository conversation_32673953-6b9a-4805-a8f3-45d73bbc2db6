<view class="container">
  <view class="upload-area" wx:if="{{!tempFilePath}}">
    <view class="upload-icon">📷</view>
    <text class="upload-text">点击选择照片</text>
    <text class="upload-desc">支持JPG、PNG、GIF格式，最大10MB</text>
    <button class="upload-btn" bindtap="chooseImage">选择照片</button>
  </view>

  <view class="preview-area" wx:if="{{tempFilePath}}">
    <image class="preview-image" src="{{tempFilePath}}" mode="aspectFit"></image>
    
    <view class="file-info" wx:if="{{fileInfo}}">
      <view class="info-item">
        <text class="info-label">文件名：</text>
        <input 
          class="filename-input" 
          value="{{customFileName}}"
          bindinput="onFileNameChange"
          placeholder="{{fileInfo.nameWithoutExt}}"
          disabled="{{uploading}}"
        />
      </view>
      <view class="info-item">
        <text class="info-label">大小：</text>
        <text class="info-value">{{fileInfo.sizeText}}</text>
      </view>
    </view>

    <view class="upload-progress" wx:if="{{uploading}}">
      <progress percent="{{uploadProgress}}" show-info stroke-width="6" activeColor="#07c160"/>
      <text class="progress-text">上传中... {{uploadProgress}}%</text>
    </view>

    <view class="action-buttons">
      <button 
        class="btn btn-secondary" 
        bindtap="chooseImage"
        disabled="{{uploading}}"
      >
        重新选择
      </button>
      <button 
        class="btn btn-primary" 
        bindtap="uploadPhoto"
        disabled="{{uploading}}"
      >
        {{uploading ? '上传中...' : '开始上传'}}
      </button>
    </view>
  </view>

  <view class="tips">
    <view class="tip-item">
      <text class="tip-icon">💡</text>
      <text class="tip-text">上传的照片会安全存储在云端</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">🔒</text>
      <text class="tip-text">只有您可以查看和下载</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">⚡</text>
      <text class="tip-text">下载后照片会自动删除</text>
    </view>
  </view>
</view>
