const request = require("../../utils/request");

Page({
  data: {
    tempFilePath: "",
    fileInfo: null,
    uploading: false,
    uploadProgress: 0,
    customFileName: "",
  },

  onLoad() {
    this.checkAuth();
  },

  checkAuth() {
    const token = wx.getStorageSync("token");
    if (!token) {
      wx.showModal({
        title: "提示",
        content: "请先登录",
        showCancel: false,
        success: () => {
          wx.redirectTo({
            url: "/pages/login/login",
          });
        },
      });
    }
  },

  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ["original", "compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 获取文件信息
        wx.getFileInfo({
          filePath: tempFilePath,
          success: (fileRes) => {
            // 检查文件大小
            if (fileRes.size > 10 * 1024 * 1024) {
              wx.showToast({
                title: "文件不能超过10MB",
                icon: "none",
              });
              return;
            }

            // 获取文件名和扩展名
            const fileName = tempFilePath.split("/").pop();
            const fileExt = fileName.substring(fileName.lastIndexOf("."));
            const fileNameWithoutExt = fileName.substring(
              0,
              fileName.lastIndexOf(".")
            );

            this.setData({
              tempFilePath,
              fileInfo: {
                name: fileName,
                nameWithoutExt: fileNameWithoutExt,
                ext: fileExt,
                size: fileRes.size,
                sizeText: this.formatFileSize(fileRes.size),
              },
              customFileName: fileNameWithoutExt,
            });
          },
        });
      },
    });
  },

  onFileNameChange(e) {
    const value = e.detail.value;
    // 如果输入为空，使用空字符串，而不是限制
    if (value === "") {
      this.setData({
        customFileName: "",
      });
      return;
    }
    // 移除文件名中的特殊字符
    const cleanedValue = value.replace(/[^a-zA-Z0-9\u4e00-\u9fa5_\-\s]/g, "");
    this.setData({
      customFileName: cleanedValue,
    });
  },

  uploadPhoto() {
    if (!this.data.tempFilePath || this.data.uploading) return;

    this.setData({
      uploading: true,
      uploadProgress: 0,
    });

    // 如果用户没有输入或清空了文件名，使用原始文件名（不含扩展名）
    const fileName =
      this.data.customFileName.trim() || this.data.fileInfo.nameWithoutExt;

    // 构建最终的文件名（文件名 + 原扩展名）
    const finalFileName = fileName + this.data.fileInfo.ext;

    const uploadTask = wx.uploadFile({
      url: `${request.baseUrl}/photos/upload`,
      filePath: this.data.tempFilePath,
      name: "photo",
      formData: {
        customFileName: finalFileName, // 传递自定义文件名
      },
      header: {
        Authorization: `Bearer ${wx.getStorageSync("token")}`,
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const data = JSON.parse(res.data);
          wx.showToast({
            title: "上传成功",
            icon: "success",
          });

          // 清空选择
          this.setData({
            tempFilePath: "",
            fileInfo: null,
            uploadProgress: 0,
            customFileName: "",
          });

          // 延迟跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: "/pages/index/index",
            });
          }, 1500);
        } else {
          wx.showToast({
            title: "上传失败",
            icon: "none",
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: "上传失败",
          icon: "none",
        });
      },
      complete: () => {
        this.setData({ uploading: false });
      },
    });

    // 监听上传进度
    uploadTask.onProgressUpdate((res) => {
      this.setData({
        uploadProgress: res.progress,
      });
    });
  },

  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },
});
