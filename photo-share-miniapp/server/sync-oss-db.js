// 同步数据库与OSS中的照片状态
require("dotenv").config();
const OSS = require("ali-oss");
const db = require("./src/config/database");

const client = new OSS({
  region: process.env.OSS_REGION,
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
  bucket: process.env.OSS_BUCKET,
  secure: true,
});

// 检查输入参数
const isDryRun = !process.argv.includes("--force");
if (isDryRun) {
  console.log("📢 正在以预览模式运行，不会实际修改数据");
  console.log("📢 若要实际执行修改，请使用 --force 参数");
}

async function syncOssAndDb() {
  try {
    console.log("🔄 开始同步数据库与OSS状态...");

    // 1. 检查数据库标记为活跃但OSS中不存在的照片
    console.log("\n📊 检查数据库中标记为活跃但OSS中不存在的照片:");
    const [activePhotos] = await db.query(
      "SELECT id, user_id, original_name, oss_key, upload_time FROM photos WHERE is_deleted = FALSE"
    );

    console.log(`发现 ${activePhotos.length} 张标记为活跃的照片记录`);

    let missingInOssCount = 0;
    for (const photo of activePhotos) {
      try {
        await client.head(photo.oss_key);
        // 文件存在，继续下一个
      } catch (error) {
        if (error.code === "NoSuchKey") {
          missingInOssCount++;
          console.log(
            `⚠️ ID:${photo.id}, ${photo.original_name} - 在数据库中标记为活跃，但在OSS中不存在`
          );

          if (!isDryRun) {
            try {
              await db.query(
                "UPDATE photos SET is_deleted = TRUE, deleted_at = NOW() WHERE id = ?",
                [photo.id]
              );
              console.log(`✅ 已将照片标记为已删除: ID=${photo.id}`);
            } catch (dbError) {
              console.error(`❌ 更新数据库失败: ID=${photo.id}`, dbError);
            }
          }
        } else {
          console.error(`❓ 检查OSS文件失败: ${photo.oss_key}`, error);
        }
      }
    }

    if (missingInOssCount === 0) {
      console.log("✅ 没有发现数据库中活跃但OSS中不存在的照片");
    } else {
      console.log(
        `⚠️ 发现 ${missingInOssCount} 张数据库中活跃但OSS中不存在的照片`
      );
    }

    // 2. 检查数据库标记为已删除但OSS中仍存在的照片
    console.log("\n📊 检查数据库中标记为已删除但OSS中仍存在的照片:");
    const [deletedPhotos] = await db.query(
      "SELECT id, user_id, original_name, oss_key, upload_time, deleted_at FROM photos WHERE is_deleted = TRUE"
    );

    console.log(`发现 ${deletedPhotos.length} 张标记为已删除的照片记录`);

    let existsInOssCount = 0;
    for (const photo of deletedPhotos) {
      try {
        await client.head(photo.oss_key);
        // 如果能执行到这里，说明文件仍然存在
        existsInOssCount++;
        const timeSinceDeleted = new Date() - new Date(photo.deleted_at);
        const minutesSinceDeleted = Math.floor(timeSinceDeleted / (1000 * 60));

        console.log(
          `⚠️ ID:${photo.id}, ${photo.original_name} - 在数据库中标记为已删除(${minutesSinceDeleted}分钟前)，但在OSS中仍存在`
        );

        if (!isDryRun) {
          try {
            await client.delete(photo.oss_key);
            console.log(`✅ 成功删除OSS文件: ${photo.oss_key}`);
          } catch (ossError) {
            console.error(`❌ 删除OSS文件失败: ${photo.oss_key}`, ossError);
          }
        }
      } catch (error) {
        if (error.code !== "NoSuchKey") {
          console.error(`❓ 检查OSS文件失败: ${photo.oss_key}`, error);
        }
        // 文件不存在是正常的，不需要处理
      }
    }

    if (existsInOssCount === 0) {
      console.log("✅ 没有发现数据库中已删除但OSS中仍存在的照片");
    } else {
      console.log(
        `⚠️ 发现 ${existsInOssCount} 张数据库中已删除但OSS中仍存在的照片`
      );
    }

    // 3. 检查OSS中存在但数据库中没有记录的照片
    console.log("\n📊 检查OSS中存在但数据库中没有记录的照片:");

    // 获取OSS中所有照片文件
    const ossFiles = [];
    let nextMarker = null;
    let truncated = false;

    do {
      const listParams = {
        prefix: "photos/",
        "max-keys": 1000,
      };

      if (nextMarker) {
        listParams.marker = nextMarker;
      }

      const result = await client.list(listParams);
      if (result.objects) {
        ossFiles.push(...result.objects);
      }

      nextMarker = result.nextMarker;
      truncated = result.isTruncated;
    } while (truncated);

    console.log(`OSS中共有 ${ossFiles.length} 个文件`);

    // 获取数据库中所有记录的OSS Key
    const [allDbPhotos] = await db.query("SELECT oss_key FROM photos");
    const dbOssKeys = allDbPhotos.map((p) => p.oss_key);

    // 找出只在OSS中存在的文件
    const orphanedOssFiles = ossFiles.filter(
      (file) => !dbOssKeys.includes(file.name)
    );

    if (orphanedOssFiles.length === 0) {
      console.log("✅ 没有发现OSS中存在但数据库中没有记录的照片");
    } else {
      console.log(
        `⚠️ 发现 ${orphanedOssFiles.length} 个OSS中存在但数据库中没有记录的照片:`
      );

      for (const file of orphanedOssFiles) {
        console.log(
          `- ${file.name} (${file.size} bytes, ${file.lastModified})`
        );

        if (!isDryRun) {
          try {
            await client.delete(file.name);
            console.log(`✅ 成功删除孤立的OSS文件: ${file.name}`);
          } catch (ossError) {
            console.error(`❌ 删除孤立的OSS文件失败: ${file.name}`, ossError);
          }
        }
      }
    }

    // 总结
    const totalIssues =
      missingInOssCount + existsInOssCount + orphanedOssFiles.length;
    console.log("\n📝 同步总结:");

    if (totalIssues === 0) {
      console.log("✅ 数据库与OSS状态完全一致，没有发现任何问题");
    } else {
      console.log(`⚠️ 发现 ${totalIssues} 个问题:`);
      console.log(`- 数据库活跃但OSS不存在: ${missingInOssCount} 条`);
      console.log(`- 数据库已删除但OSS仍存在: ${existsInOssCount} 条`);
      console.log(`- OSS存在但数据库无记录: ${orphanedOssFiles.length} 条`);

      if (isDryRun) {
        console.log("\n📢 要修复这些问题，请使用 --force 参数重新运行此脚本");
      } else {
        console.log("\n📢 已尝试修复上述问题");
      }
    }
  } catch (error) {
    console.error("❌ 同步过程中出错:", error);
  } finally {
    process.exit(0);
  }
}

syncOssAndDb();
