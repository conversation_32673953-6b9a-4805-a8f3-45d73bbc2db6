const WebSocket = require("ws");
const jwt = require("jsonwebtoken");
const config = require("../config");
const logger = require("../utils/logger");

class PhotoSyncWebSocket {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // userId -> ws connection
  }

  initialize(server) {
    this.wss = new WebSocket.Server({
      server,
      path: "/ws/photo-sync",
    });

    this.wss.on("connection", (ws, req) => {
      logger.info("新的WebSocket连接建立");

      ws.isAlive = true;
      ws.on("pong", () => {
        ws.isAlive = true;
      });

      ws.on("message", async (message) => {
        try {
          const data = JSON.parse(message);

          if (data.type === "auth") {
            // 验证token
            const token = data.token;
            if (!token) {
              ws.send(
                JSON.stringify({
                  type: "error",
                  message: "缺少认证token",
                })
              );
              ws.close();
              return;
            }

            try {
              const decoded = jwt.verify(token, config.jwt.secret);
              const userId = decoded.userId;

              // 保存连接
              if (this.clients.has(userId)) {
                // 关闭旧连接
                const oldWs = this.clients.get(userId);
                oldWs.close();
              }

              this.clients.set(userId, ws);
              ws.userId = userId;

              ws.send(
                JSON.stringify({
                  type: "auth_success",
                  userId: userId,
                })
              );

              logger.info(`用户 ${userId} WebSocket认证成功`);
            } catch (error) {
              ws.send(
                JSON.stringify({
                  type: "error",
                  message: "认证失败",
                })
              );
              ws.close();
            }
          }
        } catch (error) {
          logger.error("处理WebSocket消息错误:", error);
        }
      });

      ws.on("close", () => {
        if (ws.userId && this.clients.get(ws.userId) === ws) {
          this.clients.delete(ws.userId);
          logger.info(`用户 ${ws.userId} WebSocket连接关闭`);
        }
      });

      ws.on("error", (error) => {
        logger.error("WebSocket错误:", error);
      });
    });

    // 心跳检测
    this.startHeartbeat();

    logger.info("WebSocket服务已启动");
  }

  startHeartbeat() {
    setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          if (ws.userId) {
            this.clients.delete(ws.userId);
          }
          return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // 30秒心跳
  }

  // 发送照片数量更新
  sendPhotoCountUpdate(userId, data) {
    const ws = this.clients.get(userId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          type: "photo_count_update",
          data: {
            totalPhotos: data.totalPhotos,
            totalSize: data.totalSize,
            ossPhotos: data.ossPhotos,
            ossSize: data.ossSize,
            isConsistent: data.isConsistent,
            timestamp: new Date().toISOString(),
          },
        })
      );

      logger.info(`已向用户 ${userId} 推送照片数量更新`);
    }
  }

  // 发送同步状态
  sendSyncStatus(userId, status) {
    const ws = this.clients.get(userId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          type: "sync_status",
          data: {
            status: status,
            timestamp: new Date().toISOString(),
          },
        })
      );
    }
  }

  // 广播给所有连接的用户
  broadcast(message) {
    this.wss.clients.forEach((ws) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    });
  }
}

// 创建单例
const photoSyncWS = new PhotoSyncWebSocket();

module.exports = photoSyncWS;
