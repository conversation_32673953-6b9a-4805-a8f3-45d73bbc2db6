const cron = require("node-cron");
const syncService = require("../services/syncService");
const logger = require("../utils/logger");

class SyncTask {
  constructor() {
    this.task = null;
    this.isRunning = false;
  }

  start() {
    // 每5分钟执行一次 (*/5 * * * *)
    this.task = cron.schedule("*/5 * * * *", async () => {
      if (this.isRunning) {
        logger.sync("定时同步任务仍在执行中，跳过此次调度");
        return;
      }

      this.isRunning = true;

      try {
        logger.sync("定时同步任务开始执行");
        const startTime = Date.now();

        const result = await syncService.syncAllUsers();

        const duration = Date.now() - startTime;
        logger.sync(
          `定时同步任务完成 - 耗时: ${duration}ms, 结果: ${JSON.stringify(
            result
          )}`
        );

        // 如果失败数量过多，记录警告
        if (result.failCount > result.successCount * 0.1) {
          logger.warn(
            `定时同步任务失败率过高: ${result.failCount}/${
              result.successCount + result.failCount
            }`
          );
        }
      } catch (error) {
        logger.error("定时同步任务执行失败:", error);
      } finally {
        this.isRunning = false;
      }
    });

    logger.info("定时同步任务已启动 - 每5分钟执行一次");

    // 启动时立即执行一次
    this.runOnce();
  }

  stop() {
    if (this.task) {
      this.task.stop();
      logger.info("定时同步任务已停止");
    }
  }

  // 手动执行一次
  async runOnce() {
    if (this.isRunning) {
      logger.sync("同步任务正在执行中");
      return;
    }

    this.isRunning = true;

    try {
      logger.sync("手动触发同步任务");
      const result = await syncService.syncAllUsers();
      logger.sync(`手动同步完成: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      logger.error("手动同步失败:", error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  // 获取任务状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      isScheduled: this.task !== null,
      nextExecution: this.task ? this.task.nextDates(1)[0] : null,
    };
  }
}

// 创建单例
const syncTask = new SyncTask();

module.exports = syncTask;
