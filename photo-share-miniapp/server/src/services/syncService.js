const db = require("../config/database");
const ossClient = require("../config/oss");
const logger = require("../utils/logger");
const photoSyncWS = require("../websocket/photoSync");

class SyncService {
  constructor() {
    this.syncInProgress = new Map(); // userId -> boolean
  }

  // 同步指定用户的照片数量
  async syncPhotoCount(userId, retryCount = 0) {
    // 防止重复同步
    if (this.syncInProgress.get(userId)) {
      logger.sync(`用户 ${userId} 的同步正在进行中，跳过此次请求`);
      return null;
    }

    this.syncInProgress.set(userId, true);

    try {
      logger.sync(`开始同步用户 ${userId} 的照片数量`);

      // 获取数据库统计
      const [dbStatsResult] = await db.query(
        "SELECT COUNT(*) as totalPhotos, SUM(file_size) as totalSize FROM photos WHERE user_id = ? AND is_deleted = FALSE",
        [userId]
      );

      const dbStats = {
        totalPhotos: dbStatsResult[0].totalPhotos || 0,
        totalSize: dbStatsResult[0].totalSize || 0,
      };

      // 获取OSS统计
      const prefix = `photos/${userId}/`;
      let ossStats = {
        totalPhotos: 0,
        totalSize: 0,
      };

      try {
        const ossFiles = [];
        let nextMarker = null;
        let truncated = false;

        do {
          const listParams = {
            prefix: prefix,
            "max-keys": 1000,
          };

          if (nextMarker) {
            listParams.marker = nextMarker;
          }

          const result = await ossClient.list(listParams);
          if (result.objects) {
            ossFiles.push(...result.objects);
          }

          nextMarker = result.nextMarker;
          truncated = result.isTruncated;
        } while (truncated);

        ossStats = {
          totalPhotos: ossFiles.length,
          totalSize: ossFiles.reduce((sum, file) => sum + file.size, 0),
        };

        logger.oss(
          `OSS统计完成 - 用户: ${userId}, 文件数: ${ossStats.totalPhotos}`
        );
      } catch (ossError) {
        logger.error("获取OSS统计失败:", ossError);

        // 重试机制
        if (retryCount < 3) {
          logger.sync(`OSS查询失败，尝试重试 (${retryCount + 1}/3)`);
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * (retryCount + 1))
          );
          this.syncInProgress.delete(userId);
          return this.syncPhotoCount(userId, retryCount + 1);
        }

        throw ossError;
      }

      // 检查一致性
      const isConsistent = dbStats.totalPhotos === ossStats.totalPhotos;

      const syncResult = {
        database: dbStats,
        oss: ossStats,
        isConsistent,
        timestamp: new Date().toISOString(),
      };

      logger.sync(
        `同步结果 - 用户: ${userId}, 数据库: ${dbStats.totalPhotos}, OSS: ${ossStats.totalPhotos}, 一致: ${isConsistent}`
      );

      // 推送实时更新
      photoSyncWS.sendPhotoCountUpdate(userId, {
        totalPhotos: dbStats.totalPhotos,
        totalSize: dbStats.totalSize,
        ossPhotos: ossStats.totalPhotos,
        ossSize: ossStats.totalSize,
        isConsistent,
      });

      // 如果不一致，尝试自动修复
      if (!isConsistent) {
        logger.sync(`检测到数据不一致，尝试自动修复 - 用户: ${userId}`);
        await this.checkAndFixInconsistency(userId);
      }

      return syncResult;
    } catch (error) {
      logger.error(`同步用户 ${userId} 的照片数量失败:`, error);
      throw error;
    } finally {
      this.syncInProgress.delete(userId);
    }
  }

  // 检查并修复数据不一致
  async checkAndFixInconsistency(userId) {
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 1. 获取数据库中标记为活跃但OSS中不存在的照片
      const [activePhotos] = await connection.query(
        "SELECT id, oss_key FROM photos WHERE user_id = ? AND is_deleted = FALSE",
        [userId]
      );

      let fixedCount = 0;

      for (const photo of activePhotos) {
        try {
          await ossClient.head(photo.oss_key);
          // 文件存在，继续
        } catch (error) {
          if (error.code === "NoSuchKey") {
            // 文件不存在，标记为已删除
            await connection.query(
              "UPDATE photos SET is_deleted = TRUE, deleted_at = NOW() WHERE id = ?",
              [photo.id]
            );
            fixedCount++;
            logger.sync(`修复: 标记照片 ${photo.id} 为已删除（OSS中不存在）`);
          }
        }
      }

      // 2. 获取数据库中标记为已删除但OSS中仍存在的照片
      const [deletedPhotos] = await connection.query(
        "SELECT id, oss_key FROM photos WHERE user_id = ? AND is_deleted = TRUE",
        [userId]
      );

      for (const photo of deletedPhotos) {
        try {
          await ossClient.head(photo.oss_key);
          // 文件仍存在，删除它
          await ossClient.delete(photo.oss_key);
          fixedCount++;
          logger.oss(
            `修复: 删除OSS文件 ${photo.oss_key}（数据库中已标记删除）`
          );
        } catch (error) {
          if (error.code !== "NoSuchKey") {
            logger.error(`检查OSS文件失败: ${photo.oss_key}`, error);
          }
        }
      }

      await connection.commit();

      if (fixedCount > 0) {
        logger.sync(
          `数据一致性修复完成 - 用户: ${userId}, 修复项: ${fixedCount}`
        );

        // 修复后重新同步
        await this.syncPhotoCount(userId);
      }

      return fixedCount;
    } catch (error) {
      await connection.rollback();
      logger.error(`修复数据一致性失败 - 用户: ${userId}`, error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 全量同步所有用户
  async syncAllUsers() {
    try {
      logger.sync("开始全量同步所有用户");

      const [users] = await db.query("SELECT DISTINCT user_id FROM photos");

      let successCount = 0;
      let failCount = 0;

      for (const user of users) {
        try {
          await this.syncPhotoCount(user.user_id);
          successCount++;
        } catch (error) {
          failCount++;
          logger.error(`同步用户 ${user.user_id} 失败:`, error);
        }
      }

      logger.sync(`全量同步完成 - 成功: ${successCount}, 失败: ${failCount}`);

      return { successCount, failCount };
    } catch (error) {
      logger.error("全量同步失败:", error);
      throw error;
    }
  }
}

// 创建单例
const syncService = new SyncService();

module.exports = syncService;
