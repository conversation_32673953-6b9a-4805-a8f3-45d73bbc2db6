const winston = require("winston");
const path = require("path");
const fs = require("fs");

// 确保日志目录存在
const logDir = path.join(__dirname, "../../logs");
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 创建winston logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: winston.format.combine(
    winston.format.timestamp({
      format: "YYYY-MM-DD HH:mm:ss",
    }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: "photo-share" },
  transports: [
    // 写入错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, "error.log"),
      level: "error",
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 写入所有日志文件
    new winston.transports.File({
      filename: path.join(logDir, "combined.log"),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 写入同步专用日志
    new winston.transports.File({
      filename: path.join(logDir, "sync.log"),
      level: "info",
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          if (
            (message && message.includes("同步")) ||
            message.includes("OSS")
          ) {
            return `${timestamp} [${level.toUpperCase()}]: ${message} ${
              Object.keys(meta).length ? JSON.stringify(meta) : ""
            }`;
          }
          return null;
        }),
        winston.format.uncolorize()
      ),
    }),
  ],
});

// 开发环境下，同时输出到控制台
if (process.env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return `${timestamp} [${level}]: ${message} ${
            Object.keys(meta).length ? JSON.stringify(meta) : ""
          }`;
        })
      ),
    })
  );
}

// 创建一个专门用于同步操作的logger方法
logger.sync = (message, meta = {}) => {
  logger.info(`[SYNC] ${message}`, meta);
};

// 创建一个专门用于OSS操作的logger方法
logger.oss = (message, meta = {}) => {
  logger.info(`[OSS] ${message}`, meta);
};

// 监控日志，如果错误级别日志过多，发送告警
let errorCount = 0;
let lastAlertTime = Date.now();

logger.on("data", (log) => {
  if (log.level === "error") {
    errorCount++;

    // 如果5分钟内错误超过10个，且距离上次告警超过30分钟
    if (errorCount > 10 && Date.now() - lastAlertTime > 30 * 60 * 1000) {
      console.error("⚠️ 警告：过去5分钟内出现大量错误日志，请检查系统状态！");
      lastAlertTime = Date.now();
    }
  }
});

// 每5分钟重置错误计数
setInterval(() => {
  errorCount = 0;
}, 5 * 60 * 1000);

module.exports = logger;
