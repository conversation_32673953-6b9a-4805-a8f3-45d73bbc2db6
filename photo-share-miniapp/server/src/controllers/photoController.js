const multer = require("multer");
const path = require("path");
const fs = require("fs").promises;
const crypto = require("crypto");
const config = require("../config");
const db = require("../config/database");
const ossClient = require("../config/oss");
const syncService = require("../services/syncService");
const logger = require("../utils/logger");

// Multer配置
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    if (config.upload.allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("不支持的文件类型"));
    }
  },
}).single("photo");

// 上传照片
exports.uploadPhoto = async (req, res) => {
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }

    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({ error: "请选择要上传的照片" });
      }

      // 获取自定义文件名（如果提供）
      const customFileName = req.body.customFileName;
      let originalName = file.originalname;

      // 如果提供了自定义文件名，使用它
      if (customFileName && customFileName.trim() !== "") {
        // 验证文件扩展名是否一致
        const originalExt = path.extname(file.originalname).toLowerCase();
        const customExt = path.extname(customFileName).toLowerCase();

        if (customExt && customExt !== originalExt) {
          return res.status(400).json({ error: "文件扩展名不匹配" });
        }

        // 使用自定义文件名
        originalName = customFileName;
      }

      // 生成唯一文件名用于存储
      const ext = path.extname(originalName);
      // 清理文件名，移除特殊字符，保留中文、字母、数字、下划线、横线
      const cleanName = path
        .basename(originalName, ext)
        .replace(/[^a-zA-Z0-9\u4e00-\u9fa5_\-]/g, "_")
        .substring(0, 50); // 限制长度
      const timestamp = Date.now();
      const hash = crypto.randomBytes(8).toString("hex");
      // 使用清理后的原始名称 + 时间戳 + 短hash，确保唯一性
      const filename = `${cleanName}_${timestamp}_${hash}${ext}`;
      const ossKey = `photos/${req.user.id}/${filename}`;

      // 上传到OSS
      const result = await ossClient.put(ossKey, file.buffer, {
        headers: {
          "Content-Type": file.mimetype,
          "Content-Disposition": `inline; filename="${encodeURIComponent(
            originalName
          )}"`,
        },
      });

      // 保存到数据库（使用自定义的或原始的文件名）
      const [dbResult] = await db.query(
        `INSERT INTO photos (user_id, filename, original_name, oss_key, file_size, mime_type) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
        [
          req.user.id,
          filename,
          originalName, // 使用自定义文件名或原始文件名
          ossKey,
          file.size,
          file.mimetype,
        ]
      );

      logger.info(
        `照片上传成功 - 用户: ${req.user.id}, 文件名: ${originalName}, OSS Key: ${ossKey}`
      );

      res.json({
        success: true,
        photoId: dbResult.insertId,
        filename: originalName,
        size: file.size,
      });

      // 触发实时同步
      syncService.syncPhotoCount(req.user.id).catch((error) => {
        logger.error(`上传后同步失败 - 用户: ${req.user.id}`, error);
      });
    } catch (error) {
      logger.error("上传错误:", error);
      console.error("上传错误:", error);
      res.status(500).json({ error: "上传失败" });
    }
  });
};

// 获取照片列表
exports.getPhotos = async (req, res) => {
  try {
    const { page = 1, pageSize = 20 } = req.query;
    const offset = (page - 1) * pageSize;

    // 查询照片列表
    const [photos] = await db.query(
      `SELECT id, original_name, file_size, mime_type, upload_time, oss_key
             FROM photos
             WHERE user_id = ? AND is_deleted = FALSE
             ORDER BY upload_time DESC
             LIMIT ? OFFSET ?`,
      [req.user.id, parseInt(pageSize), offset]
    );

    // 查询总数
    const [countResult] = await db.query(
      "SELECT COUNT(*) as total FROM photos WHERE user_id = ? AND is_deleted = FALSE",
      [req.user.id]
    );

    // 为每张照片生成预览URL
    const photosWithUrls = await Promise.all(
      photos.map(async (photo) => {
        const thumbnailUrl = ossClient.signatureUrl(photo.oss_key, {
          expires: 3600,
          process: "image/resize,m_fill,h_200,w_200,limit_0",
        });

        return {
          ...photo,
          thumbnailUrl,
          sizeText: formatFileSize(photo.file_size),
        };
      })
    );

    res.json({
      photos: photosWithUrls,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / pageSize),
      },
    });
  } catch (error) {
    console.error("获取照片列表错误:", error);
    res.status(500).json({ error: "获取照片列表失败" });
  }
};

// 获取照片预览URL
exports.getPhotoPreviewUrl = async (req, res) => {
  try {
    const { id } = req.params;

    // 验证照片所有权
    const [photos] = await db.query(
      "SELECT * FROM photos WHERE id = ? AND user_id = ? AND is_deleted = FALSE",
      [id, req.user.id]
    );

    if (photos.length === 0) {
      return res.status(404).json({ error: "照片不存在" });
    }

    const photo = photos[0];

    // 生成预览URL（大图）
    const previewUrl = ossClient.signatureUrl(photo.oss_key, {
      expires: 3600,
      process: "image/resize,w_1080,limit_0",
    });

    // 生成原图URL
    const originalUrl = ossClient.signatureUrl(photo.oss_key, {
      expires: 3600,
    });

    res.json({
      previewUrl,
      originalUrl,
      photoInfo: {
        name: photo.original_name,
        size: photo.file_size,
        uploadTime: photo.upload_time,
      },
    });
  } catch (error) {
    console.error("获取预览URL错误:", error);
    res.status(500).json({ error: "获取预览URL失败" });
  }
};

// 下载并删除照片
exports.downloadAndDelete = async (req, res) => {
  const connection = await db.getConnection();

  try {
    const { id } = req.params;
    console.log(`开始处理照片下载请求: 照片ID=${id}, 用户ID=${req.user.id}`);

    await connection.beginTransaction();

    // 验证照片所有权
    const [photos] = await connection.query(
      "SELECT * FROM photos WHERE id = ? AND user_id = ? AND is_deleted = FALSE FOR UPDATE",
      [id, req.user.id]
    );

    if (photos.length === 0) {
      console.log(`照片不存在或已被删除: 照片ID=${id}, 用户ID=${req.user.id}`);
      await connection.rollback();
      return res.status(404).json({ error: "照片不存在" });
    }

    const photo = photos[0];
    console.log(`找到照片记录: 照片ID=${id}, OSS Key=${photo.oss_key}`);

    // 生成下载URL - 阿里云OSS的签名URL在删除对象后仍然有效
    const downloadUrl = ossClient.signatureUrl(photo.oss_key, {
      expires: 300, // 5分钟有效期
      response: {
        "content-disposition": `attachment; filename="${encodeURIComponent(
          photo.original_name
        )}"`,
      },
    });
    console.log(`生成下载URL成功: 照片ID=${id}, 有效期=5分钟`);

    // 标记为已删除
    await connection.query(
      "UPDATE photos SET is_deleted = TRUE, deleted_at = NOW() WHERE id = ?",
      [id]
    );
    console.log(`数据库中标记照片为已删除: 照片ID=${id}`);

    // 记录下载日志
    await connection.query(
      "INSERT INTO download_logs (photo_id, user_id) VALUES (?, ?)",
      [id, req.user.id]
    );
    console.log(`记录下载日志成功: 照片ID=${id}, 用户ID=${req.user.id}`);

    await connection.commit();
    console.log(`数据库事务提交成功: 照片ID=${id}`);

    // 立即删除OSS文件
    try {
      console.log(`开始删除OSS文件: ${photo.oss_key}`);
      logger.oss(`开始删除文件: ${photo.oss_key}`);
      await ossClient.delete(photo.oss_key);
      console.log(`✅ 成功删除OSS文件: ${photo.oss_key}`);
      logger.oss(`成功删除文件: ${photo.oss_key}`);
    } catch (ossError) {
      // 即使OSS删除失败，也不影响用户下载体验，只记录错误日志
      console.error(`❌ 删除OSS文件失败: ${photo.oss_key}`, ossError);
      logger.error(`删除OSS文件失败: ${photo.oss_key}`, ossError);

      // 尝试检查文件是否仍然存在
      try {
        await ossClient.head(photo.oss_key);
        console.error(`⚠️ 文件仍然存在于OSS中: ${photo.oss_key}, 需要手动清理`);
        logger.warn(`文件仍然存在于OSS中: ${photo.oss_key}`);
      } catch (headError) {
        if (headError.code === "NoSuchKey") {
          console.log(`✅ 文件实际已从OSS中删除: ${photo.oss_key}`);
          logger.oss(`文件实际已删除: ${photo.oss_key}`);
        } else {
          console.error(`❓ 无法检查文件状态: ${photo.oss_key}`, headError);
          logger.error(`无法检查文件状态: ${photo.oss_key}`, headError);
        }
      }
    }

    // 触发实时同步
    syncService.syncPhotoCount(req.user.id).catch((error) => {
      logger.error(`下载删除后同步失败 - 用户: ${req.user.id}`, error);
    });

    res.json({
      downloadUrl,
      message: "请在5分钟内完成下载，文件已从云端删除",
    });
  } catch (error) {
    await connection.rollback();
    console.error("❌ 下载删除错误:", error);
    res.status(500).json({ error: "操作失败" });
  } finally {
    connection.release();
  }
};

// 批量删除照片
exports.deletePhotos = async (req, res) => {
  const connection = await db.getConnection();

  try {
    const { photoIds } = req.body;

    if (!Array.isArray(photoIds) || photoIds.length === 0) {
      return res.status(400).json({ error: "请选择要删除的照片" });
    }

    console.log(
      `开始批量删除照片: 用户ID=${req.user.id}, 照片数量=${
        photoIds.length
      }, 照片IDs=${photoIds.join(",")}`
    );

    await connection.beginTransaction();

    // 查询要删除的照片
    const [photos] = await connection.query(
      "SELECT * FROM photos WHERE id IN (?) AND user_id = ? AND is_deleted = FALSE",
      [photoIds, req.user.id]
    );

    if (photos.length === 0) {
      console.log(
        `没有找到可删除的照片: 用户ID=${
          req.user.id
        }, 请求删除IDs=${photoIds.join(",")}`
      );
      await connection.rollback();
      return res.status(404).json({ error: "没有找到可删除的照片" });
    }

    console.log(`找到${photos.length}张照片可以删除, 用户ID=${req.user.id}`);

    // 标记为已删除
    await connection.query(
      "UPDATE photos SET is_deleted = TRUE, deleted_at = NOW() WHERE id IN (?)",
      [photos.map((p) => p.id)]
    );
    console.log(`数据库中标记${photos.length}张照片为已删除`);

    await connection.commit();
    console.log(`数据库事务提交成功`);

    // 异步删除OSS文件
    let successCount = 0;
    let failedCount = 0;

    for (const photo of photos) {
      try {
        console.log(`开始删除OSS文件: ${photo.oss_key}`);
        await ossClient.delete(photo.oss_key);
        console.log(`✅ 成功删除OSS文件: ${photo.oss_key}`);
        successCount++;
      } catch (error) {
        console.error(`❌ 删除OSS文件失败: ${photo.oss_key}`, error);
        failedCount++;

        // 尝试检查文件是否仍然存在
        try {
          await ossClient.head(photo.oss_key);
          console.error(
            `⚠️ 文件仍然存在于OSS中: ${photo.oss_key}, 需要手动清理`
          );
        } catch (headError) {
          if (headError.code === "NoSuchKey") {
            console.log(`✅ 文件实际已从OSS中删除: ${photo.oss_key}`);
          } else {
            console.error(`❓ 无法检查文件状态: ${photo.oss_key}`, headError);
          }
        }
      }
    }

    console.log(
      `批量删除OSS文件完成: 成功=${successCount}, 失败=${failedCount}`
    );
    logger.sync(
      `批量删除OSS文件完成 - 用户: ${req.user.id}, 成功: ${successCount}, 失败: ${failedCount}`
    );

    // 触发实时同步
    syncService.syncPhotoCount(req.user.id).catch((error) => {
      logger.error(`批量删除后同步失败 - 用户: ${req.user.id}`, error);
    });

    res.json({
      success: true,
      deletedCount: photos.length,
    });
  } catch (error) {
    await connection.rollback();
    console.error("❌ 批量删除错误:", error);
    res.status(500).json({ error: "删除失败" });
  } finally {
    connection.release();
  }
};

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 获取OSS照片统计信息
exports.getOssPhotoStats = async (req, res) => {
  try {
    // 获取用户在数据库中的照片数量
    const [dbStatsResult] = await db.query(
      "SELECT COUNT(*) as totalPhotos, SUM(file_size) as totalSize FROM photos WHERE user_id = ? AND is_deleted = FALSE",
      [req.user.id]
    );

    const dbStats = {
      totalPhotos: dbStatsResult[0].totalPhotos || 0,
      totalSize: dbStatsResult[0].totalSize || 0,
      totalSizeFormatted: formatFileSize(dbStatsResult[0].totalSize || 0),
    };

    // 获取OSS中的实际照片数量
    const prefix = `photos/${req.user.id}/`;
    let ossStats = {
      totalPhotos: 0,
      totalSize: 0,
      totalSizeFormatted: "0 Bytes",
    };

    try {
      // OSS查询可能会抛出异常，使用try-catch包装
      const result = await ossClient.list({
        prefix: prefix,
        "max-keys": 1000, // 每次最多获取1000个
      });

      if (result.objects && result.objects.length > 0) {
        const totalSize = result.objects.reduce(
          (sum, obj) => sum + obj.size,
          0
        );
        ossStats = {
          totalPhotos: result.objects.length,
          totalSize: totalSize,
          totalSizeFormatted: formatFileSize(totalSize),
        };
      }
    } catch (ossError) {
      console.error("获取OSS照片统计失败:", ossError);
      // 即使OSS查询失败，也返回数据库中的统计信息
    }

    res.json({
      database: dbStats,
      oss: ossStats,
      // 提供一致性检查信息
      isConsistent: dbStats.totalPhotos === ossStats.totalPhotos,
    });
  } catch (error) {
    console.error("获取照片统计信息错误:", error);
    res.status(500).json({ error: "获取照片统计信息失败" });
  }
};

// 获取OSS照片预览列表
exports.getOssPhotoPreviews = async (req, res) => {
  try {
    console.log(`获取OSS照片预览: 用户ID=${req.user.id}`);

    // 获取用户的OSS目录前缀
    const prefix = `photos/${req.user.id}/`;
    console.log(`OSS前缀: ${prefix}`);

    // 从OSS中获取所有照片
    const ossFiles = [];
    let nextMarker = null;
    let truncated = false;

    do {
      const listParams = {
        prefix: prefix,
        "max-keys": 1000,
      };

      if (nextMarker) {
        listParams.marker = nextMarker;
      }

      const result = await ossClient.list(listParams);
      if (result.objects) {
        ossFiles.push(...result.objects);
      }

      nextMarker = result.nextMarker;
      truncated = result.isTruncated;
    } while (truncated);

    console.log(`OSS中找到 ${ossFiles.length} 个文件`);

    // 为每个文件生成预览URL
    const previews = await Promise.all(
      ossFiles.map(async (file) => {
        // 获取文件名
        const filename = file.name.split("/").pop();

        // 生成预览URL
        const thumbnailUrl = ossClient.signatureUrl(file.name, {
          expires: 3600,
          process: "image/resize,m_fill,h_200,w_200,limit_0",
        });

        // 生成原图URL
        const originalUrl = ossClient.signatureUrl(file.name, {
          expires: 3600,
        });

        return {
          name: filename,
          ossKey: file.name,
          size: file.size,
          lastModified: file.lastModified,
          thumbnailUrl,
          originalUrl,
          sizeText: formatFileSize(file.size),
        };
      })
    );

    res.json({
      ossPhotos: previews,
      total: previews.length,
    });
  } catch (error) {
    console.error("获取OSS照片预览错误:", error);
    res.status(500).json({ error: "获取OSS照片预览失败" });
  }
};
