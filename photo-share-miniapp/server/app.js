const express = require("express");
const http = require("http");
const cors = require("cors");
const helmet = require("helmet");
const compression = require("compression");
const rateLimit = require("express-rate-limit");
const config = require("./src/config");
const photoSyncWS = require("./src/websocket/photoSync");
const syncTask = require("./src/tasks/syncTask");
const logger = require("./src/utils/logger");

const app = express();
const server = http.createServer(app);

// 初始化WebSocket服务
photoSyncWS.initialize(server);

// 安全中间件
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求记录中间件
app.use((req, res, next) => {
  logger.info(`[${req.method}] ${req.url}`);
  console.log(`${new Date().toISOString()} [${req.method}] ${req.url}`);
  next();
});

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制100个请求
});
app.use("/api/", limiter);

// 上传限制
const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 50, // 限制50次上传
});
app.use("/api/photos/upload", uploadLimiter);

// 路由
app.use("/api/auth", require("./src/routes/auth"));
app.use("/api/photos", require("./src/routes/photos"));

// 同步任务管理API
app.get("/api/sync/status", (req, res) => {
  res.json(syncTask.getStatus());
});

app.post("/api/sync/trigger", async (req, res) => {
  try {
    const result = await syncTask.runOnce();
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 路由日志记录，用于调试
console.log("📋 已注册的路由：");
app._router.stack.forEach((middleware) => {
  if (middleware.route) {
    // 路由中间件
    console.log(
      `   ${Object.keys(middleware.route.methods).join(",")} ${
        middleware.route.path
      }`
    );
  } else if (middleware.name === "router") {
    // 路由器中间件
    middleware.handle.stack.forEach((handler) => {
      if (handler.route) {
        console.log(
          `   ${Object.keys(handler.route.methods).join(",")} ${
            handler.route.path
          }`
        );
      }
    });
  }
});

// 健康检查
app.get("/health", (req, res) => {
  res.json({ status: "ok", timestamp: new Date().toISOString() });
});

// 404处理
app.use((req, res, next) => {
  res.status(404).json({ error: "接口不存在", path: req.url });
});

// 错误处理
app.use((err, req, res, next) => {
  logger.error(`服务器错误: ${err.message}`, err);
  console.error(`❌ 错误: ${err.message}`);
  console.error(err.stack);
  res.status(500).json({ error: "服务器内部错误" });
});

// 启动服务器
const PORT = config.port;
server.listen(PORT, () => {
  console.log(`🚀 服务器运行在端口 ${PORT}`);
  logger.info(`服务器启动成功，端口: ${PORT}`);

  // 启动定时同步任务
  syncTask.start();
});

// 优雅关闭
process.on("SIGTERM", () => {
  logger.info("收到SIGTERM信号，开始优雅关闭...");

  syncTask.stop();

  server.close(() => {
    logger.info("服务器已关闭");
    process.exit(0);
  });
});
