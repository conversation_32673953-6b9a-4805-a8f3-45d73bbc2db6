{"name": "triple-beam", "version": "1.4.1", "description": "Definitions of levels for logging purposes & shareable Symbol constants.", "main": "index.js", "scripts": {"lint": "eslint config/*.js index.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "pretest": "npm run lint", "test": "nyc mocha test.js"}, "repository": {"type": "git", "url": "git+https://github.com/winstonjs/triple-beam.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "winston", "logging", "logform", "symbols", "logs", "levels"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/winstonjs/triple-beam/issues"}, "homepage": "https://github.com/winstonjs/triple-beam#readme", "devDependencies": {"assume": "^2.0.1", "@dabh/eslint-config-populist": "^5.0.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "typescript": "^5.1.6"}, "engines": {"node": ">= 14.0.0"}}