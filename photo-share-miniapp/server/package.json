{"name": "photo-share-server", "version": "1.0.0", "description": "Photo sharing mini-app backend server", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["photo", "sharing", "miniapp", "wechat"], "author": "", "license": "ISC", "dependencies": {"ali-oss": "^6.17.1", "axios": "^1.5.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "node-cron": "^4.1.0", "winston": "^3.17.0", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.0.1"}}