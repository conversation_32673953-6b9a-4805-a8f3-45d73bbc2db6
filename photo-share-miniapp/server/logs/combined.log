{"level":"info","message":"WebSocket服务已启动","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"定时同步任务已启动 - 每5分钟执行一次","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"[SYNC] 手动触发同步任务","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"[SYNC] 开始全量同步所有用户","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:25:44"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 3","service":"photo-share","timestamp":"2025-06-06 20:25:45"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 0, OSS: 3, 一致: false","service":"photo-share","timestamp":"2025-06-06 20:25:45"}
{"level":"info","message":"已向用户 2 推送照片数量更新","service":"photo-share","timestamp":"2025-06-06 20:25:45"}
{"level":"info","message":"[SYNC] 检测到数据不一致，尝试自动修复 - 用户: 2","service":"photo-share","timestamp":"2025-06-06 20:25:45"}
{"level":"info","message":"[OSS] 修复: 删除OSS文件 photos/2/1749209500944-da4f91a41725cae05c78e0d14ed1e1a5.jpg（数据库中已标记删除）","service":"photo-share","timestamp":"2025-06-06 20:25:46"}
{"level":"info","message":"[OSS] 修复: 删除OSS文件 photos/2/1749209508210-926ec828306355d0576fac46f94e84ce.jpg（数据库中已标记删除）","service":"photo-share","timestamp":"2025-06-06 20:25:46"}
{"level":"info","message":"[OSS] 修复: 删除OSS文件 photos/2/1749209522451-f7faa79425773d7df6bcf3ba7f222a23.png（数据库中已标记删除）","service":"photo-share","timestamp":"2025-06-06 20:25:46"}
{"level":"info","message":"[SYNC] 数据一致性修复完成 - 用户: 2, 修复项: 3","service":"photo-share","timestamp":"2025-06-06 20:25:47"}
{"level":"info","message":"[SYNC] 用户 2 的同步正在进行中，跳过此次请求","service":"photo-share","timestamp":"2025-06-06 20:25:47"}
{"level":"info","message":"[SYNC] 全量同步完成 - 成功: 1, 失败: 0","service":"photo-share","timestamp":"2025-06-06 20:25:47"}
{"level":"info","message":"[SYNC] 手动同步完成: {\"successCount\":1,\"failCount\":0}","service":"photo-share","timestamp":"2025-06-06 20:25:47"}
{"level":"info","message":"用户 2 WebSocket连接关闭","service":"photo-share","timestamp":"2025-06-06 20:25:51"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:25:52"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:25:52"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:25:52"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:25:52"}
{"level":"info","message":"[POST] /api/photos/upload","service":"photo-share","timestamp":"2025-06-06 20:26:15"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:26:16"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 1","service":"photo-share","timestamp":"2025-06-06 20:26:16"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 1, OSS: 1, 一致: true","service":"photo-share","timestamp":"2025-06-06 20:26:16"}
{"level":"info","message":"已向用户 2 推送照片数量更新","service":"photo-share","timestamp":"2025-06-06 20:26:16"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:26:17"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:26:17"}
{"level":"info","message":"[POST] /api/photos/7/download","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"[OSS] 开始删除文件: photos/2/1749212775054-a8bba360d9134ad5485c882cbd50793d.jpg","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"[OSS] 成功删除文件: photos/2/1749212775054-a8bba360d9134ad5485c882cbd50793d.jpg","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 0","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 0, OSS: 0, 一致: true","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"已向用户 2 推送照片数量更新","service":"photo-share","timestamp":"2025-06-06 20:26:30"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:27:27"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:27:27"}
{"level":"info","message":"用户 2 WebSocket连接关闭","service":"photo-share","timestamp":"2025-06-06 20:28:34"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:28:34"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:28:34"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:28:34"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:28:34"}
{"level":"info","message":"用户 2 WebSocket连接关闭","service":"photo-share","timestamp":"2025-06-06 20:28:48"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:28:48"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:28:48"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:28:48"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:28:48"}
{"level":"info","message":"[SYNC] 定时同步任务开始执行","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"[SYNC] 开始全量同步所有用户","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 0","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 0, OSS: 0, 一致: true","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"已向用户 2 推送照片数量更新","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"[SYNC] 全量同步完成 - 成功: 1, 失败: 0","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"[SYNC] 定时同步任务完成 - 耗时: 690ms, 结果: {\"successCount\":1,\"failCount\":0}","service":"photo-share","timestamp":"2025-06-06 20:30:00"}
{"level":"info","message":"用户 2 WebSocket连接关闭","service":"photo-share","timestamp":"2025-06-06 20:30:59"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:31:02"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:31:02"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:31:02"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:31:02"}
{"level":"info","message":"WebSocket服务已启动","service":"photo-share","timestamp":"2025-06-06 20:31:52"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"photo-share","timestamp":"2025-06-06 20:31:52"}
{"level":"info","message":"定时同步任务已启动 - 每5分钟执行一次","service":"photo-share","timestamp":"2025-06-06 20:31:52"}
{"level":"info","message":"[SYNC] 手动触发同步任务","service":"photo-share","timestamp":"2025-06-06 20:31:52"}
{"level":"info","message":"[SYNC] 开始全量同步所有用户","service":"photo-share","timestamp":"2025-06-06 20:31:52"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:31:52"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 0","service":"photo-share","timestamp":"2025-06-06 20:31:53"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 0, OSS: 0, 一致: true","service":"photo-share","timestamp":"2025-06-06 20:31:53"}
{"level":"info","message":"[SYNC] 全量同步完成 - 成功: 1, 失败: 0","service":"photo-share","timestamp":"2025-06-06 20:31:53"}
{"level":"info","message":"[SYNC] 手动同步完成: {\"successCount\":1,\"failCount\":0}","service":"photo-share","timestamp":"2025-06-06 20:31:53"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:31:55"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:31:55"}
{"level":"info","message":"用户 2 WebSocket连接关闭","service":"photo-share","timestamp":"2025-06-06 20:31:57"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:31:58"}
{"level":"info","message":"新的WebSocket连接建立","service":"photo-share","timestamp":"2025-06-06 20:31:58"}
{"level":"info","message":"用户 2 WebSocket认证成功","service":"photo-share","timestamp":"2025-06-06 20:31:58"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:31:58"}
{"level":"info","message":"[POST] /api/photos/upload","service":"photo-share","timestamp":"2025-06-06 20:32:37"}
{"level":"info","message":"照片上传成功 - 用户: 2, 文件名: 1111.jpg, OSS Key: photos/2/1749213157277-a689cefec815c1e21c2a93889a75a0ce.jpg","service":"photo-share","timestamp":"2025-06-06 20:32:38"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:32:38"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 1","service":"photo-share","timestamp":"2025-06-06 20:32:38"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 1, OSS: 1, 一致: true","service":"photo-share","timestamp":"2025-06-06 20:32:38"}
{"level":"info","message":"已向用户 2 推送照片数量更新","service":"photo-share","timestamp":"2025-06-06 20:32:38"}
{"level":"info","message":"[GET] /api/photos?page=1&pageSize=20","service":"photo-share","timestamp":"2025-06-06 20:32:39"}
{"level":"info","message":"[GET] /api/photos/stats","service":"photo-share","timestamp":"2025-06-06 20:32:39"}
{"level":"info","message":"[SYNC] 定时同步任务开始执行","service":"photo-share","timestamp":"2025-06-06 20:35:00"}
{"level":"info","message":"[SYNC] 开始全量同步所有用户","service":"photo-share","timestamp":"2025-06-06 20:35:00"}
{"level":"info","message":"[SYNC] 开始同步用户 2 的照片数量","service":"photo-share","timestamp":"2025-06-06 20:35:00"}
{"level":"info","message":"[OSS] OSS统计完成 - 用户: 2, 文件数: 1","service":"photo-share","timestamp":"2025-06-06 20:35:01"}
{"level":"info","message":"[SYNC] 同步结果 - 用户: 2, 数据库: 1, OSS: 1, 一致: true","service":"photo-share","timestamp":"2025-06-06 20:35:01"}
{"level":"info","message":"已向用户 2 推送照片数量更新","service":"photo-share","timestamp":"2025-06-06 20:35:01"}
{"level":"info","message":"[SYNC] 全量同步完成 - 成功: 1, 失败: 0","service":"photo-share","timestamp":"2025-06-06 20:35:01"}
{"level":"info","message":"[SYNC] 定时同步任务完成 - 耗时: 1104ms, 结果: {\"successCount\":1,\"failCount\":0}","service":"photo-share","timestamp":"2025-06-06 20:35:01"}
