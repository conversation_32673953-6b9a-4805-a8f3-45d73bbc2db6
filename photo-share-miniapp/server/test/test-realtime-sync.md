# 实时同步功能测试指南

## 测试前准备

1. 确保服务器已启动

```bash
cd photo-share-miniapp/server
npm start
```

2. 确保小程序已编译并在模拟器/真机运行

## 测试场景

### 1. WebSocket 连接测试

- 打开小程序首页
- 查看控制台日志，应看到：
  - "WebSocket 连接成功"
  - "WebSocket 认证成功"
- 检查页面右上角是否显示连接状态图标

### 2. 照片上传实时同步测试

- 步骤：
  1. 打开小程序上传页面
  2. 上传一张照片
  3. 返回首页
- 预期结果：
  - 无需刷新页面，照片数量自动更新
  - OSS 数量与数据库数量保持一致

### 3. 照片下载删除实时同步测试

- 步骤：
  1. 点击任意照片的下载按钮
  2. 确认下载
- 预期结果：
  - 照片下载到本地相册
  - 页面照片数量自动减 1
  - OSS 文件被删除
  - 数据保持一致

### 4. 批量删除实时同步测试

- 步骤：
  1. 选择多张照片
  2. 点击批量删除
  3. 确认删除
- 预期结果：
  - 选中的照片从列表消失
  - 照片数量实时更新
  - OSS 文件全部删除

### 5. 定时同步测试

- 步骤：
  1. 手动在 OSS 控制台删除一个文件
  2. 等待 5 分钟
- 预期结果：
  - 定时任务自动执行
  - 数据库中对应记录被标记为已删除
  - 小程序收到数量更新通知

### 6. 断线重连测试

- 步骤：
  1. 关闭服务器
  2. 观察小程序控制台
  3. 重启服务器
- 预期结果：
  - 小程序检测到断线
  - 自动尝试重连（最多 5 次）
  - 重连成功后恢复实时同步

### 7. 手动触发同步测试

- 使用 API 测试工具（如 Postman）：

```
POST http://localhost:3000/api/sync/trigger
```

- 预期结果：
  - 返回同步结果
  - 所有用户的数据得到同步

## 日志查看

### 服务器日志

```bash
# 查看实时日志
tail -f logs/combined.log

# 查看同步专用日志
tail -f logs/sync.log

# 查看错误日志
tail -f logs/error.log
```

### 小程序日志

- 在微信开发者工具的 Console 面板查看
- 搜索关键词：WebSocket、同步、OSS

## 常见问题

1. **WebSocket 连接失败**

   - 检查服务器是否启动
   - 检查防火墙设置
   - 确认 token 有效

2. **数据不一致**

   - 查看 sync.log 了解同步详情
   - 手动触发同步 API
   - 检查 OSS 权限配置

3. **实时更新延迟**
   - 检查网络状况
   - 查看 WebSocket 连接状态
   - 确认服务器性能正常
