# Ubuntu 服务器部署指南

本文档详细说明如何在 Ubuntu 服务器上部署照片分享小程序后端服务。

## 系统要求

- Ubuntu 20.04 LTS 或更高版本
- 至少 1GB RAM
- 至少 10GB 可用磁盘空间
- 开放端口：3000（API 服务）、3306（MySQL）

## 部署步骤

### 1. 更新系统

```bash
sudo apt update
sudo apt upgrade -y
```

### 2. 安装 Node.js 18.x

```bash
# 安装 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装 Node.js
sudo apt install -y nodejs

# 验证安装
node --version
npm --version
```

### 3. 安装 MySQL 8.0

```bash
# 安装 MySQL
sudo apt install -y mysql-server

# 安全配置
sudo mysql_secure_installation

# 登录 MySQL
sudo mysql -u root -p
```

创建数据库和用户：

```sql
-- 创建数据库
CREATE DATABASE photo_share_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'photo_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授予权限
GRANT ALL PRIVILEGES ON photo_share_db.* TO 'photo_user'@'localhost';
FLUSH PRIVILEGES;

-- 使用数据库
USE photo_share_db;

-- 导入数据库结构（从 database.sql 文件）
SOURCE /path/to/photo-share-miniapp/server/database.sql;
```

### 4. 安装 PM2

```bash
# 全局安装 PM2
sudo npm install -g pm2

# 设置 PM2 开机自启
pm2 startup systemd
# 执行输出的命令
```

### 5. 配置防火墙

```bash
# 允许 SSH
sudo ufw allow 22/tcp

# 允许 API 端口
sudo ufw allow 3000/tcp

# 如果使用 Nginx
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 启用防火墙
sudo ufw enable
```

### 6. 安装 Nginx（可选，用于反向代理）

```bash
# 安装 Nginx
sudo apt install -y nginx

# 创建配置文件
sudo nano /etc/nginx/sites-available/photo-share
```

Nginx 配置内容：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket 支持
    location /ws/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：

```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/photo-share /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

### 7. 部署应用

```bash
# 创建应用目录
sudo mkdir -p /var/www/photo-share
sudo chown -R $USER:$USER /var/www/photo-share

# 克隆或上传代码
cd /var/www/photo-share
# 使用 git clone 或 scp 上传代码

# 进入服务器目录
cd photo-share-miniapp/server

# 安装依赖
npm install --production

# 创建环境变量文件
cp .env.example .env
nano .env
```

配置 `.env` 文件：

```env
# 服务器配置
PORT=3000
NODE_ENV=production

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=photo_user
DB_PASSWORD=your_secure_password
DB_NAME=photo_share_db

# 阿里云OSS配置
OSS_REGION=oss-cn-shanghai
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_BUCKET=your_bucket_name

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 微信小程序配置
WX_APPID=your_appid
WX_SECRET=your_secret

# 日志级别
LOG_LEVEL=info
```

### 8. 创建日志目录

```bash
# 创建日志目录
mkdir -p logs
chmod 755 logs
```

### 9. 使用 PM2 启动应用

```bash
# 使用 ecosystem 配置文件启动
pm2 start ecosystem.config.js

# 保存 PM2 进程列表
pm2 save

# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 监控
pm2 monit
```

### 10. 配置 SSL 证书（使用 Let's Encrypt）

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo certbot renew --dry-run
```

## 维护命令

### 应用管理

```bash
# 重启应用
pm2 restart photo-share

# 停止应用
pm2 stop photo-share

# 查看日志
pm2 logs photo-share

# 清除日志
pm2 flush

# 更新应用
cd /var/www/photo-share/photo-share-miniapp/server
git pull
npm install --production
pm2 restart photo-share
```

### 数据库备份

```bash
# 创建备份脚本
sudo nano /usr/local/bin/backup-photo-share.sh
```

备份脚本内容：

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/photo-share"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="photo_share_db"
DB_USER="photo_user"
DB_PASS="your_secure_password"

mkdir -p $BACKUP_DIR
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/backup_$DATE.sql
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
```

设置定时备份：

```bash
# 添加执行权限
sudo chmod +x /usr/local/bin/backup-photo-share.sh

# 添加定时任务
sudo crontab -e
# 添加以下行（每天凌晨2点备份）
0 2 * * * /usr/local/bin/backup-photo-share.sh
```

### 监控

```bash
# 查看系统资源
htop

# 查看磁盘空间
df -h

# 查看内存使用
free -m

# 查看 PM2 监控
pm2 monit

# 查看应用日志
tail -f /var/www/photo-share/photo-share-miniapp/server/logs/combined.log
```

## 故障排查

### 1. 应用无法启动

```bash
# 检查端口占用
sudo lsof -i :3000

# 查看错误日志
pm2 logs --err

# 检查环境变量
pm2 env 0
```

### 2. 数据库连接失败

```bash
# 检查 MySQL 状态
sudo systemctl status mysql

# 测试连接
mysql -u photo_user -p -h localhost photo_share_db
```

### 3. WebSocket 连接失败

```bash
# 检查 Nginx 配置
sudo nginx -t

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

## 安全建议

1. **定期更新系统**

   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **配置 fail2ban**

   ```bash
   sudo apt install -y fail2ban
   sudo systemctl enable fail2ban
   ```

3. **限制 SSH 访问**

   - 使用密钥认证
   - 禁用密码登录
   - 更改默认端口

4. **定期备份**

   - 数据库自动备份
   - 代码仓库备份
   - OSS 数据备份

5. **监控告警**
   - 设置系统监控
   - 配置错误告警
   - 定期检查日志

## 性能优化

1. **数据库优化**

   - 添加适当的索引
   - 定期优化表
   - 调整 MySQL 配置

2. **Node.js 优化**

   - 使用集群模式
   - 配置适当的内存限制
   - 启用 Gzip 压缩

3. **Nginx 优化**
   - 启用缓存
   - 配置 Gzip
   - 优化 buffer 大小

## 联系支持

如遇到问题，请检查：

1. 应用日志：`/var/www/photo-share/photo-share-miniapp/server/logs/`
2. PM2 日志：`pm2 logs`
3. 系统日志：`/var/log/syslog`
